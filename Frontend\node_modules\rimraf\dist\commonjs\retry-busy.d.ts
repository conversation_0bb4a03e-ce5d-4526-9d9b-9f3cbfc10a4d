import { RimrafAsyncOptions, RimrafOptions } from './index.js';
export declare const MAXBACKOFF = 200;
export declare const RATE = 1.2;
export declare const MAXRETRIES = 10;
export declare const codes: Set<string>;
export declare const retryBusy: (fn: (path: string) => Promise<any>) => (path: string, opt: RimrafAsyncOptions, backoff?: number, total?: number) => Promise<any>;
export declare const retryBusySync: (fn: (path: string) => any) => (path: string, opt: RimrafOptions) => any;
//# sourceMappingURL=retry-busy.d.ts.map