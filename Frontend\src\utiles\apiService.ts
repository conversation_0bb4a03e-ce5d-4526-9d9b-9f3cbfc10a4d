// Servicio centralizado para llamadas a la API
const API_BASE_URL = 'http://localhost:8080/api';

interface ApiResponse<T> {
  data: T;
  success: boolean;
  error?: string;
}

class ApiService {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private pendingRequests = new Map<string, Promise<any>>();

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    cacheTTL: number = 0
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;
    const cacheKey = `${url}-${JSON.stringify(options)}`;

    // Verificar caché si está habilitado
    if (cacheTTL > 0) {
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < cached.ttl) {
        return { data: cached.data, success: true };
      }
    }

    // Evitar solicitudes duplicadas
    if (this.pendingRequests.has(cacheKey)) {
      const data = await this.pendingRequests.get(cacheKey);
      return { data, success: true };
    }

    try {
      const requestPromise = fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      }).then(async (response) => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      });

      this.pendingRequests.set(cacheKey, requestPromise);
      const data = await requestPromise;

      // Guardar en caché si está habilitado
      if (cacheTTL > 0) {
        this.cache.set(cacheKey, {
          data,
          timestamp: Date.now(),
          ttl: cacheTTL,
        });
      }

      this.pendingRequests.delete(cacheKey);
      return { data, success: true };
    } catch (error) {
      this.pendingRequests.delete(cacheKey);
      return {
        data: null as T,
        success: false,
        error: error instanceof Error ? error.message : 'Error desconocido',
      };
    }
  }

  // Métodos específicos para cada entidad
  async getEstablecimientos(useCache = true) {
    return this.request('/establecimiento', {}, useCache ? 5 * 60 * 1000 : 0);
  }

  async getAgricultores(useCache = true) {
    return this.request('/agricultor', {}, useCache ? 5 * 60 * 1000 : 0);
  }

  async getServicios(useCache = true) {
    return this.request('/servicio', {}, useCache ? 2 * 60 * 1000 : 0);
  }

  async getInsumos(useCache = true) {
    return this.request('/insumo', {}, useCache ? 10 * 60 * 1000 : 0);
  }

  async getTareas(useCache = true) {
    return this.request('/tarea', {}, useCache ? 1 * 60 * 1000 : 0);
  }

  // Método para obtener datos del dashboard en paralelo
  async getDashboardData() {
    try {
      const [establecimientos, servicios, tareas] = await Promise.allSettled([
        this.getEstablecimientos(true),
        this.getServicios(true),
        this.getTareas(true),
      ]);

      return {
        establecimientos: establecimientos.status === 'fulfilled' ? establecimientos.value.data : [],
        servicios: servicios.status === 'fulfilled' ? servicios.value.data : [],
        tareas: tareas.status === 'fulfilled' ? tareas.value.data : [],
      };
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      return {
        establecimientos: [],
        servicios: [],
        tareas: [],
      };
    }
  }

  // Limpiar caché
  clearCache(pattern?: string) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  // Precargar datos importantes
  async preloadCriticalData() {
    const criticalEndpoints = [
      this.getEstablecimientos(true),
      this.getServicios(true),
    ];

    try {
      await Promise.allSettled(criticalEndpoints);
      console.log('Critical data preloaded successfully');
    } catch (error) {
      console.error('Error preloading critical data:', error);
    }
  }
}

export const apiService = new ApiService();
export default apiService;
