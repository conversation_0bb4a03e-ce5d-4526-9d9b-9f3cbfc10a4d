"use strict";Object.defineProperty(exports,"__esModule",{value:!0});exports.LRUCache=void 0;var M=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,x=new Set,R=typeof process=="object"&&process?process:{},U=(a,t,e,i)=>{typeof R.emitWarning=="function"?R.emitWarning(a,t,e,i):console.error(`[${e}] ${t}: ${a}`)},C=globalThis.AbortController,L=globalThis.AbortSignal;if(typeof C>"u"){L=class{onabort;_onabort=[];reason;aborted=!1;addEventListener(i,s){this._onabort.push(s)}},C=class{constructor(){t()}signal=new L;abort(i){if(!this.signal.aborted){this.signal.reason=i,this.signal.aborted=!0;for(let s of this.signal._onabort)s(i);this.signal.onabort?.(i)}}};let a=R.env?.LRU_CACHE_IGNORE_AC_WARNING!=="1",t=()=>{a&&(a=!1,U("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}var G=a=>!x.has(a),H=Symbol("type"),y=a=>a&&a===Math.floor(a)&&a>0&&isFinite(a),I=a=>y(a)?a<=Math.pow(2,8)?Uint8Array:a<=Math.pow(2,16)?Uint16Array:a<=Math.pow(2,32)?Uint32Array:a<=Number.MAX_SAFE_INTEGER?E:null:null,E=class extends Array{constructor(t){super(t),this.fill(0)}},W=class a{heap;length;static#l=!1;static create(t){let e=I(t);if(!e)return[];a.#l=!0;let i=new a(t,e);return a.#l=!1,i}constructor(t,e){if(!a.#l)throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}},D=class a{#l;#c;#p;#v;#w;#D;#L;#S;get perf(){return this.#S}ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#n;#_;#s;#i;#t;#a;#u;#o;#h;#m;#r;#b;#y;#d;#A;#z;#f;#x;static unsafeExposeInternals(t){return{starts:t.#y,ttls:t.#d,sizes:t.#b,keyMap:t.#s,keyList:t.#i,valList:t.#t,next:t.#a,prev:t.#u,get head(){return t.#o},get tail(){return t.#h},free:t.#m,isBackgroundFetch:e=>t.#e(e),backgroundFetch:(e,i,s,n)=>t.#M(e,i,s,n),moveToTail:e=>t.#W(e),indexes:e=>t.#F(e),rindexes:e=>t.#T(e),isStale:e=>t.#g(e)}}get max(){return this.#l}get maxSize(){return this.#c}get calculatedSize(){return this.#_}get size(){return this.#n}get fetchMethod(){return this.#D}get memoMethod(){return this.#L}get dispose(){return this.#p}get onInsert(){return this.#v}get disposeAfter(){return this.#w}constructor(t){let{max:e=0,ttl:i,ttlResolution:s=1,ttlAutopurge:n,updateAgeOnGet:h,updateAgeOnHas:o,allowStale:r,dispose:g,onInsert:_,disposeAfter:f,noDisposeOnSet:c,noUpdateTTL:u,maxSize:A=0,maxEntrySize:d=0,sizeCalculation:m,fetchMethod:l,memoMethod:w,noDeleteOnFetchRejection:b,noDeleteOnStaleGet:p,allowStaleOnFetchRejection:S,allowStaleOnFetchAbort:z,ignoreFetchAbort:F,perf:v}=t;if(v!==void 0&&typeof v?.now!="function")throw new TypeError("perf option must have a now() method if specified");if(this.#S=v??M,e!==0&&!y(e))throw new TypeError("max option must be a nonnegative integer");let T=e?I(e):Array;if(!T)throw new Error("invalid max value: "+e);if(this.#l=e,this.#c=A,this.maxEntrySize=d||this.#c,this.sizeCalculation=m,this.sizeCalculation){if(!this.#c&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(w!==void 0&&typeof w!="function")throw new TypeError("memoMethod must be a function if defined");if(this.#L=w,l!==void 0&&typeof l!="function")throw new TypeError("fetchMethod must be a function if specified");if(this.#D=l,this.#z=!!l,this.#s=new Map,this.#i=new Array(e).fill(void 0),this.#t=new Array(e).fill(void 0),this.#a=new T(e),this.#u=new T(e),this.#o=0,this.#h=0,this.#m=W.create(e),this.#n=0,this.#_=0,typeof g=="function"&&(this.#p=g),typeof _=="function"&&(this.#v=_),typeof f=="function"?(this.#w=f,this.#r=[]):(this.#w=void 0,this.#r=void 0),this.#A=!!this.#p,this.#x=!!this.#v,this.#f=!!this.#w,this.noDisposeOnSet=!!c,this.noUpdateTTL=!!u,this.noDeleteOnFetchRejection=!!b,this.allowStaleOnFetchRejection=!!S,this.allowStaleOnFetchAbort=!!z,this.ignoreFetchAbort=!!F,this.maxEntrySize!==0){if(this.#c!==0&&!y(this.#c))throw new TypeError("maxSize must be a positive integer if specified");if(!y(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");this.#V()}if(this.allowStale=!!r,this.noDeleteOnStaleGet=!!p,this.updateAgeOnGet=!!h,this.updateAgeOnHas=!!o,this.ttlResolution=y(s)||s===0?s:1,this.ttlAutopurge=!!n,this.ttl=i||0,this.ttl){if(!y(this.ttl))throw new TypeError("ttl must be a positive integer if specified");this.#G()}if(this.#l===0&&this.ttl===0&&this.#c===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#l&&!this.#c){let O="LRU_CACHE_UNBOUNDED";G(O)&&(x.add(O),U("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",O,a))}}getRemainingTTL(t){return this.#s.has(t)?1/0:0}#G(){let t=new E(this.#l),e=new E(this.#l);this.#d=t,this.#y=e,this.#j=(n,h,o=this.#S.now())=>{if(e[n]=h!==0?o:0,t[n]=h,h!==0&&this.ttlAutopurge){let r=setTimeout(()=>{this.#g(n)&&this.#O(this.#i[n],"expire")},h+1);r.unref&&r.unref()}},this.#C=n=>{e[n]=t[n]!==0?this.#S.now():0},this.#E=(n,h)=>{if(t[h]){let o=t[h],r=e[h];if(!o||!r)return;n.ttl=o,n.start=r,n.now=i||s();let g=n.now-r;n.remainingTTL=o-g}};let i=0,s=()=>{let n=this.#S.now();if(this.ttlResolution>0){i=n;let h=setTimeout(()=>i=0,this.ttlResolution);h.unref&&h.unref()}return n};this.getRemainingTTL=n=>{let h=this.#s.get(n);if(h===void 0)return 0;let o=t[h],r=e[h];if(!o||!r)return 1/0;let g=(i||s())-r;return o-g},this.#g=n=>{let h=e[n],o=t[n];return!!o&&!!h&&(i||s())-h>o}}#C=()=>{};#E=()=>{};#j=()=>{};#g=()=>!1;#V(){let t=new E(this.#l);this.#_=0,this.#b=t,this.#R=e=>{this.#_-=t[e],t[e]=0},this.#N=(e,i,s,n)=>{if(this.#e(i))return 0;if(!y(s))if(n){if(typeof n!="function")throw new TypeError("sizeCalculation must be a function");if(s=n(i,e),!y(s))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return s},this.#U=(e,i,s)=>{if(t[e]=i,this.#c){let n=this.#c-t[e];for(;this.#_>n;)this.#I(!0)}this.#_+=t[e],s&&(s.entrySize=i,s.totalCalculatedSize=this.#_)}}#R=t=>{};#U=(t,e,i)=>{};#N=(t,e,i,s)=>{if(i||s)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#F({allowStale:t=this.allowStale}={}){if(this.#n)for(let e=this.#h;!(!this.#P(e)||((t||!this.#g(e))&&(yield e),e===this.#o));)e=this.#u[e]}*#T({allowStale:t=this.allowStale}={}){if(this.#n)for(let e=this.#o;!(!this.#P(e)||((t||!this.#g(e))&&(yield e),e===this.#h));)e=this.#a[e]}#P(t){return t!==void 0&&this.#s.get(this.#i[t])===t}*entries(){for(let t of this.#F())this.#t[t]!==void 0&&this.#i[t]!==void 0&&!this.#e(this.#t[t])&&(yield[this.#i[t],this.#t[t]])}*rentries(){for(let t of this.#T())this.#t[t]!==void 0&&this.#i[t]!==void 0&&!this.#e(this.#t[t])&&(yield[this.#i[t],this.#t[t]])}*keys(){for(let t of this.#F()){let e=this.#i[t];e!==void 0&&!this.#e(this.#t[t])&&(yield e)}}*rkeys(){for(let t of this.#T()){let e=this.#i[t];e!==void 0&&!this.#e(this.#t[t])&&(yield e)}}*values(){for(let t of this.#F())this.#t[t]!==void 0&&!this.#e(this.#t[t])&&(yield this.#t[t])}*rvalues(){for(let t of this.#T())this.#t[t]!==void 0&&!this.#e(this.#t[t])&&(yield this.#t[t])}[Symbol.iterator](){return this.entries()}[Symbol.toStringTag]="LRUCache";find(t,e={}){for(let i of this.#F()){let s=this.#t[i],n=this.#e(s)?s.__staleWhileFetching:s;if(n!==void 0&&t(n,this.#i[i],this))return this.get(this.#i[i],e)}}forEach(t,e=this){for(let i of this.#F()){let s=this.#t[i],n=this.#e(s)?s.__staleWhileFetching:s;n!==void 0&&t.call(e,n,this.#i[i],this)}}rforEach(t,e=this){for(let i of this.#T()){let s=this.#t[i],n=this.#e(s)?s.__staleWhileFetching:s;n!==void 0&&t.call(e,n,this.#i[i],this)}}purgeStale(){let t=!1;for(let e of this.#T({allowStale:!0}))this.#g(e)&&(this.#O(this.#i[e],"expire"),t=!0);return t}info(t){let e=this.#s.get(t);if(e===void 0)return;let i=this.#t[e],s=this.#e(i)?i.__staleWhileFetching:i;if(s===void 0)return;let n={value:s};if(this.#d&&this.#y){let h=this.#d[e],o=this.#y[e];if(h&&o){let r=h-(this.#S.now()-o);n.ttl=r,n.start=Date.now()}}return this.#b&&(n.size=this.#b[e]),n}dump(){let t=[];for(let e of this.#F({allowStale:!0})){let i=this.#i[e],s=this.#t[e],n=this.#e(s)?s.__staleWhileFetching:s;if(n===void 0||i===void 0)continue;let h={value:n};if(this.#d&&this.#y){h.ttl=this.#d[e];let o=this.#S.now()-this.#y[e];h.start=Math.floor(Date.now()-o)}this.#b&&(h.size=this.#b[e]),t.unshift([i,h])}return t}load(t){this.clear();for(let[e,i]of t){if(i.start){let s=Date.now()-i.start;i.start=this.#S.now()-s}this.set(e,i.value,i)}}set(t,e,i={}){if(e===void 0)return this.delete(t),this;let{ttl:s=this.ttl,start:n,noDisposeOnSet:h=this.noDisposeOnSet,sizeCalculation:o=this.sizeCalculation,status:r}=i,{noUpdateTTL:g=this.noUpdateTTL}=i,_=this.#N(t,e,i.size||0,o);if(this.maxEntrySize&&_>this.maxEntrySize)return r&&(r.set="miss",r.maxEntrySizeExceeded=!0),this.#O(t,"set"),this;let f=this.#n===0?void 0:this.#s.get(t);if(f===void 0)f=this.#n===0?this.#h:this.#m.length!==0?this.#m.pop():this.#n===this.#l?this.#I(!1):this.#n,this.#i[f]=t,this.#t[f]=e,this.#s.set(t,f),this.#a[this.#h]=f,this.#u[f]=this.#h,this.#h=f,this.#n++,this.#U(f,_,r),r&&(r.set="add"),g=!1,this.#x&&this.#v?.(e,t,"add");else{this.#W(f);let c=this.#t[f];if(e!==c){if(this.#z&&this.#e(c)){c.__abortController.abort(new Error("replaced"));let{__staleWhileFetching:u}=c;u!==void 0&&!h&&(this.#A&&this.#p?.(u,t,"set"),this.#f&&this.#r?.push([u,t,"set"]))}else h||(this.#A&&this.#p?.(c,t,"set"),this.#f&&this.#r?.push([c,t,"set"]));if(this.#R(f),this.#U(f,_,r),this.#t[f]=e,r){r.set="replace";let u=c&&this.#e(c)?c.__staleWhileFetching:c;u!==void 0&&(r.oldValue=u)}}else r&&(r.set="update");this.#x&&this.onInsert?.(e,t,e===c?"update":"replace")}if(s!==0&&!this.#d&&this.#G(),this.#d&&(g||this.#j(f,s,n),r&&this.#E(r,f)),!h&&this.#f&&this.#r){let c=this.#r,u;for(;u=c?.shift();)this.#w?.(...u)}return this}pop(){try{for(;this.#n;){let t=this.#t[this.#o];if(this.#I(!0),this.#e(t)){if(t.__staleWhileFetching)return t.__staleWhileFetching}else if(t!==void 0)return t}}finally{if(this.#f&&this.#r){let t=this.#r,e;for(;e=t?.shift();)this.#w?.(...e)}}}#I(t){let e=this.#o,i=this.#i[e],s=this.#t[e];return this.#z&&this.#e(s)?s.__abortController.abort(new Error("evicted")):(this.#A||this.#f)&&(this.#A&&this.#p?.(s,i,"evict"),this.#f&&this.#r?.push([s,i,"evict"])),this.#R(e),t&&(this.#i[e]=void 0,this.#t[e]=void 0,this.#m.push(e)),this.#n===1?(this.#o=this.#h=0,this.#m.length=0):this.#o=this.#a[e],this.#s.delete(i),this.#n--,e}has(t,e={}){let{updateAgeOnHas:i=this.updateAgeOnHas,status:s}=e,n=this.#s.get(t);if(n!==void 0){let h=this.#t[n];if(this.#e(h)&&h.__staleWhileFetching===void 0)return!1;if(this.#g(n))s&&(s.has="stale",this.#E(s,n));else return i&&this.#C(n),s&&(s.has="hit",this.#E(s,n)),!0}else s&&(s.has="miss");return!1}peek(t,e={}){let{allowStale:i=this.allowStale}=e,s=this.#s.get(t);if(s===void 0||!i&&this.#g(s))return;let n=this.#t[s];return this.#e(n)?n.__staleWhileFetching:n}#M(t,e,i,s){let n=e===void 0?void 0:this.#t[e];if(this.#e(n))return n;let h=new C,{signal:o}=i;o?.addEventListener("abort",()=>h.abort(o.reason),{signal:h.signal});let r={signal:h.signal,options:i,context:s},g=(d,m=!1)=>{let{aborted:l}=h.signal,w=i.ignoreFetchAbort&&d!==void 0;if(i.status&&(l&&!m?(i.status.fetchAborted=!0,i.status.fetchError=h.signal.reason,w&&(i.status.fetchAbortIgnored=!0)):i.status.fetchResolved=!0),l&&!w&&!m)return f(h.signal.reason);let b=u;return this.#t[e]===u&&(d===void 0?b.__staleWhileFetching!==void 0?this.#t[e]=b.__staleWhileFetching:this.#O(t,"fetch"):(i.status&&(i.status.fetchUpdated=!0),this.set(t,d,r.options))),d},_=d=>(i.status&&(i.status.fetchRejected=!0,i.status.fetchError=d),f(d)),f=d=>{let{aborted:m}=h.signal,l=m&&i.allowStaleOnFetchAbort,w=l||i.allowStaleOnFetchRejection,b=w||i.noDeleteOnFetchRejection,p=u;if(this.#t[e]===u&&(!b||p.__staleWhileFetching===void 0?this.#O(t,"fetch"):l||(this.#t[e]=p.__staleWhileFetching)),w)return i.status&&p.__staleWhileFetching!==void 0&&(i.status.returnedStale=!0),p.__staleWhileFetching;if(p.__returned===p)throw d},c=(d,m)=>{let l=this.#D?.(t,n,r);l&&l instanceof Promise&&l.then(w=>d(w===void 0?void 0:w),m),h.signal.addEventListener("abort",()=>{(!i.ignoreFetchAbort||i.allowStaleOnFetchAbort)&&(d(void 0),i.allowStaleOnFetchAbort&&(d=w=>g(w,!0)))})};i.status&&(i.status.fetchDispatched=!0);let u=new Promise(c).then(g,_),A=Object.assign(u,{__abortController:h,__staleWhileFetching:n,__returned:void 0});return e===void 0?(this.set(t,A,{...r.options,status:void 0}),e=this.#s.get(t)):this.#t[e]=A,A}#e(t){if(!this.#z)return!1;let e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof C}async fetch(t,e={}){let{allowStale:i=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,ttl:h=this.ttl,noDisposeOnSet:o=this.noDisposeOnSet,size:r=0,sizeCalculation:g=this.sizeCalculation,noUpdateTTL:_=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:c=this.allowStaleOnFetchRejection,ignoreFetchAbort:u=this.ignoreFetchAbort,allowStaleOnFetchAbort:A=this.allowStaleOnFetchAbort,context:d,forceRefresh:m=!1,status:l,signal:w}=e;if(!this.#z)return l&&(l.fetch="get"),this.get(t,{allowStale:i,updateAgeOnGet:s,noDeleteOnStaleGet:n,status:l});let b={allowStale:i,updateAgeOnGet:s,noDeleteOnStaleGet:n,ttl:h,noDisposeOnSet:o,size:r,sizeCalculation:g,noUpdateTTL:_,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:c,allowStaleOnFetchAbort:A,ignoreFetchAbort:u,status:l,signal:w},p=this.#s.get(t);if(p===void 0){l&&(l.fetch="miss");let S=this.#M(t,p,b,d);return S.__returned=S}else{let S=this.#t[p];if(this.#e(S)){let O=i&&S.__staleWhileFetching!==void 0;return l&&(l.fetch="inflight",O&&(l.returnedStale=!0)),O?S.__staleWhileFetching:S.__returned=S}let z=this.#g(p);if(!m&&!z)return l&&(l.fetch="hit"),this.#W(p),s&&this.#C(p),l&&this.#E(l,p),S;let F=this.#M(t,p,b,d),T=F.__staleWhileFetching!==void 0&&i;return l&&(l.fetch=z?"stale":"refresh",T&&z&&(l.returnedStale=!0)),T?F.__staleWhileFetching:F.__returned=F}}async forceFetch(t,e={}){let i=await this.fetch(t,e);if(i===void 0)throw new Error("fetch() returned undefined");return i}memo(t,e={}){let i=this.#L;if(!i)throw new Error("no memoMethod provided to constructor");let{context:s,forceRefresh:n,...h}=e,o=this.get(t,h);if(!n&&o!==void 0)return o;let r=i(t,o,{options:h,context:s});return this.set(t,r,h),r}get(t,e={}){let{allowStale:i=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,status:h}=e,o=this.#s.get(t);if(o!==void 0){let r=this.#t[o],g=this.#e(r);return h&&this.#E(h,o),this.#g(o)?(h&&(h.get="stale"),g?(h&&i&&r.__staleWhileFetching!==void 0&&(h.returnedStale=!0),i?r.__staleWhileFetching:void 0):(n||this.#O(t,"expire"),h&&i&&(h.returnedStale=!0),i?r:void 0)):(h&&(h.get="hit"),g?r.__staleWhileFetching:(this.#W(o),s&&this.#C(o),r))}else h&&(h.get="miss")}#H(t,e){this.#u[e]=t,this.#a[t]=e}#W(t){t!==this.#h&&(t===this.#o?this.#o=this.#a[t]:this.#H(this.#u[t],this.#a[t]),this.#H(this.#h,t),this.#h=t)}delete(t){return this.#O(t,"delete")}#O(t,e){let i=!1;if(this.#n!==0){let s=this.#s.get(t);if(s!==void 0)if(i=!0,this.#n===1)this.#k(e);else{this.#R(s);let n=this.#t[s];if(this.#e(n)?n.__abortController.abort(new Error("deleted")):(this.#A||this.#f)&&(this.#A&&this.#p?.(n,t,e),this.#f&&this.#r?.push([n,t,e])),this.#s.delete(t),this.#i[s]=void 0,this.#t[s]=void 0,s===this.#h)this.#h=this.#u[s];else if(s===this.#o)this.#o=this.#a[s];else{let h=this.#u[s];this.#a[h]=this.#a[s];let o=this.#a[s];this.#u[o]=this.#u[s]}this.#n--,this.#m.push(s)}}if(this.#f&&this.#r?.length){let s=this.#r,n;for(;n=s?.shift();)this.#w?.(...n)}return i}clear(){return this.#k("delete")}#k(t){for(let e of this.#T({allowStale:!0})){let i=this.#t[e];if(this.#e(i))i.__abortController.abort(new Error("deleted"));else{let s=this.#i[e];this.#A&&this.#p?.(i,s,t),this.#f&&this.#r?.push([i,s,t])}}if(this.#s.clear(),this.#t.fill(void 0),this.#i.fill(void 0),this.#d&&this.#y&&(this.#d.fill(0),this.#y.fill(0)),this.#b&&this.#b.fill(0),this.#o=0,this.#h=0,this.#m.length=0,this.#_=0,this.#n=0,this.#f&&this.#r){let e=this.#r,i;for(;i=e?.shift();)this.#w?.(...i)}}};exports.LRUCache=D;
//# sourceMappingURL=index.min.js.map
