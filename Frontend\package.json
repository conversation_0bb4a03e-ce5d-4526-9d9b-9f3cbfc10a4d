{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000", "dev:clean": "npm run clean && next dev --port 3000", "dev:safe": "next dev --port 3001", "clean": "rimraf .next || echo 'Clean completed with warnings'", "clean:force": "rimraf .next node_modules && npm install", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mapbox/mapbox-gl-draw": "^1.5.0", "@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.2", "@mui/x-data-grid": "^8.11.1", "@turf/turf": "^7.2.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "mapbox-gl": "^3.14.0", "next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.62.0", "react-map-gl": "^8.0.4", "recharts": "^3.1.2"}, "devDependencies": {"@types/geojson": "^7946.0.16", "@types/mapbox-gl": "^3.4.1", "@types/node": "^20.0.0", "@types/react": "^18.3.24", "@types/react-dom": "^18.3.7", "@types/react-map-gl": "^6.1.7", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "rimraf": "^6.0.1", "typescript": "^5.0.0"}}