import { useState, useEffect, useCallback } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

interface UseApiCacheOptions {
  cacheTime?: number; // Tiempo en milisegundos para mantener en caché
  staleTime?: number; // Tiempo en milisegundos antes de considerar datos obsoletos
  refetchOnMount?: boolean;
}

const cache = new Map<string, CacheEntry<any>>();

export function useApiCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  options: UseApiCacheOptions = {}
) {
  const {
    cacheTime = 5 * 60 * 1000, // 5 minutos por defecto
    staleTime = 1 * 60 * 1000,  // 1 minuto por defecto
    refetchOnMount = true
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async (force = false) => {
    const now = Date.now();
    const cached = cache.get(key);

    // Si hay datos en caché y no han expirado, usarlos
    if (!force && cached && now < cached.expiry) {
      setData(cached.data);
      setLoading(false);
      return cached.data;
    }

    // Si hay datos en caché pero están obsoletos, mostrarlos mientras se actualizan
    if (cached && now < cached.timestamp + cacheTime) {
      setData(cached.data);
      setLoading(false);
    } else {
      setLoading(true);
    }

    try {
      const result = await fetcher();
      
      // Guardar en caché
      cache.set(key, {
        data: result,
        timestamp: now,
        expiry: now + staleTime
      });

      setData(result);
      setError(null);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Error desconocido');
      setError(error);
      
      // Si hay datos en caché, mantenerlos en caso de error
      if (cached) {
        setData(cached.data);
      }
      
      throw error;
    } finally {
      setLoading(false);
    }
  }, [key, fetcher, cacheTime, staleTime]);

  const refetch = useCallback(() => fetchData(true), [fetchData]);

  useEffect(() => {
    if (refetchOnMount) {
      fetchData();
    }
  }, [fetchData, refetchOnMount]);

  return {
    data,
    loading,
    error,
    refetch,
    fetchData
  };
}

// Hook específico para datos del dashboard
export function useDashboardData() {
  return useApiCache(
    'dashboard-summary',
    async () => {
      // Simular carga de datos del dashboard
      // En el futuro, reemplazar con llamada real a la API
      return {
        hectareas: '1,250 ha',
        servicios: '24',
        productividad: '94%',
        cultivos: '8 tipos'
      };
    },
    {
      cacheTime: 10 * 60 * 1000, // 10 minutos
      staleTime: 2 * 60 * 1000,  // 2 minutos
    }
  );
}

// Función para limpiar caché
export function clearCache(key?: string) {
  if (key) {
    cache.delete(key);
  } else {
    cache.clear();
  }
}
