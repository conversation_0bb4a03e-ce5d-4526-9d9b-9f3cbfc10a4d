import React, { useState, useEffect } from "react";
import {
  Card,
  Typography,
  Grid,
  CircularProgress,
  Box,
  Button,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import WbSunnyIcon from "@mui/icons-material/WbSunny";
import CloudIcon from "@mui/icons-material/Cloud";
import UmbrellaIcon from "@mui/icons-material/Umbrella";
import CloudQueueIcon from "@mui/icons-material/CloudQueue";
import AcUnitIcon from "@mui/icons-material/AcUnit";
import RefreshIcon from "@mui/icons-material/Refresh";
import OpacityIcon from "@mui/icons-material/Opacity";
import AirIcon from "@mui/icons-material/Air";
import VisibilityIcon from "@mui/icons-material/Visibility";
import ThermostatIcon from "@mui/icons-material/Thermostat";

interface WeatherData {
  current: {
    temp_c: number;
    temp_f: number;
    humidity: number;
    wind_kph: number;
    vis_km: number;
    feelslike_c: number;
    feelslike_f: number;
    condition: {
      text: string;
    };
  };
  forecast: {
    forecastday: Array<{
      day: {
        avgtemp_c: number;
        maxtemp_c: number;
        mintemp_c: number;
        daily_chance_of_rain: number;
        condition: {
          text: string;
        };
      };
      date: string;
    }>;
  };
  location?: {
    name: string;
    region: string;
    country: string;
  };
}

const StyledContainer = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  backgroundColor: "#ffffff",
  borderRadius: "16px",
  border: "none",
  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
  marginBottom: theme.spacing(2),
  maxWidth: "400px",
  margin: "0 auto",
}));

const WeatherDetailItem = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  gap: theme.spacing(1),
  padding: theme.spacing(1, 0),
}));

const ForecastContainer = styled(Box)(({ theme }) => ({
  marginTop: theme.spacing(2),
}));

const ForecastCard = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  padding: theme.spacing(1.5, 0),
  borderBottom: "1px solid #f0f0f0",
  "&:last-child": {
    borderBottom: "none",
  },
}));

const getWeatherIcon = (condition: string) => {
  const conditionLower = condition.toLowerCase();

  if (conditionLower.includes("sol") || conditionLower.includes("despejado")) {
    return <WbSunnyIcon sx={{ fontSize: 40, color: "#FFD700" }} />; // Amarillo dorado para sol
  } else if (conditionLower.includes("lluvia")) {
    return <UmbrellaIcon sx={{ fontSize: 40, color: "#4682B4" }} />; // Azul acero para lluvia
  } else if (
    conditionLower.includes("nublado") ||
    conditionLower.includes("nuboso")
  ) {
    return <CloudIcon sx={{ fontSize: 40, color: "#808080" }} />; // Gris para nubes
  } else if (conditionLower.includes("niebla")) {
    return <CloudQueueIcon sx={{ fontSize: 40, color: "#B8B8B8" }} />; // Gris claro para niebla
  } else if (conditionLower.includes("nieve")) {
    return <AcUnitIcon sx={{ fontSize: 40, color: "#E0FFFF" }} />; // Celeste claro para nieve
  }
  return <WbSunnyIcon sx={{ fontSize: 40, color: "#FFD700" }} />; // Por defecto
};

const WeatherDashboard = () => {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [tempUnit, setTempUnit] = useState<"c" | "f">("c");
  // Eliminamos estos estados ya que no los necesitaremos más
  // const [manualLocation, setManualLocation] = useState<string>("");
  // const [showLocationInput, setShowLocationInput] = useState<boolean>(false);

  // API key de WeatherAPI.com
  const API_KEY = "a80d2077f58948f8ac7193110250804";

  const fetchWeatherData = async (latitude: number, longitude: number) => {
    setLoading(true);
    try {
      if (!isValidCoordinate(latitude, longitude)) {
        throw new Error("Coordenadas inválidas");
      }

      const url = `https://api.weatherapi.com/v1/forecast.json?key=${API_KEY}&q=${latitude},${longitude}&days=7&lang=es`;
      console.log("URL de la API:", url);
      const response = await fetch(url);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`Error del servidor: ${response.status}`);
      }

      const data = await response.json();

      setWeatherData(data);
      setLoading(false);
      setError(null);
    } catch (err) {
      console.error("Error en fetchWeatherData:", err);
      setError(
        err instanceof Error
          ? err.message
          : "Error al cargar los datos del clima"
      );
      setLoading(false);
    }
  };

  const fetchWeatherDataByCity = async (city: string) => {
    setLoading(true);
    try {
      const url = `https://api.weatherapi.com/v1/forecast.json?key=${API_KEY}&q=${encodeURIComponent(
        city
      )}&days=7&lang=es`;
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`Error del servidor: ${response.status}`);
      }

      const data = await response.json();
      setWeatherData(data);
      setLoading(false);
      setError(null);
      // Guardar la ubicación en localStorage
      localStorage.setItem("lastKnownLocation", city);
    } catch (err) {
      setError("No se encontró la ubicación. Verifica el nombre ingresado.");
      setLoading(false);
    }
  };

  // Función para validar coordenadas
  const isValidCoordinate = (lat: number, lon: number) => {
    return lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180;
  };

  const MAX_RETRIES = 3;
  const RETRY_DELAY = 2000; // 2 segundos

  const getGeolocationAndFetch = (retryCount = 0) => {
    console.log(
      `Intento ${retryCount + 1} de ${MAX_RETRIES} para obtener ubicación`
    );

    if (navigator.geolocation) {
      const options = {
        enableHighAccuracy: true,
        timeout: 30000, // Aumentado a 30 segundos
        maximumAge: 0, // No usar cache
      };

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          console.log("Coordenadas obtenidas:", {
            latitude,
            longitude,
            accuracy,
            timestamp: new Date(position.timestamp).toISOString(),
          });

          // Solo reintentamos si la precisión es realmente mala (más de 20km)
          if (accuracy > 20000 && retryCount < MAX_RETRIES) {
            console.log(
              `Precisión insuficiente (${accuracy}m), reintentando...`
            );
            setLoading(true);
            setTimeout(
              () => getGeolocationAndFetch(retryCount + 1),
              RETRY_DELAY
            );
            return;
          }

          // Usar las coordenadas GPS sin importar la precisión si es el último intento
          if (isValidCoordinate(latitude, longitude)) {
            const lat = Number(latitude.toFixed(4));
            const lon = Number(longitude.toFixed(4));
            fetchWeatherData(lat, lon);
          } else {
            setError("Coordenadas de ubicación inválidas");
            setLoading(false);
          }
        },
        async (err: GeolocationPositionError) => {
          console.error("Error de geolocalización:", {
            code: err.code,
            message: err.message,
          });

          // Solo usar IP como último recurso después de varios intentos fallidos
          if (retryCount < MAX_RETRIES) {
            console.log(
              `Reintentando obtener GPS... Intento ${retryCount + 1}`
            );
            setTimeout(
              () => getGeolocationAndFetch(retryCount + 1),
              RETRY_DELAY
            );
            return;
          }

          // Solo usamos IP como último recurso
          try {
            const ipResponse = await fetch("https://ipapi.co/json/");
            const ipData = await ipResponse.json();

            if (ipData.latitude && ipData.longitude) {
              console.log("Usando ubicación por IP como último recurso");
              fetchWeatherData(ipData.latitude, ipData.longitude);
              return;
            }
          } catch (error) {
            console.error("Error al obtener ubicación por IP:", error);
          }

          let errorMessage = "Error al obtener la ubicación. Por favor: \n";
          errorMessage += "1. Verifica que el GPS esté activado\n";
          errorMessage +=
            "2. Permite el acceso a la ubicación en tu navegador\n";
          errorMessage +=
            "3. Asegúrate de tener buena señal GPS y conexión a internet";

          setError(errorMessage);
          setLoading(false);
        },
        options
      );
    } else {
      setError("La geolocalización no es soportada por este navegador.");
      setLoading(false);
    }
  };

  useEffect(() => {
    let mounted = true;

    const initializeWeather = async () => {
      if (mounted) {
        // Intentar usar la última ubicación conocida
        const lastLocation = localStorage.getItem("lastKnownLocation");
        if (lastLocation) {
          await fetchWeatherDataByCity(lastLocation);
        } else {
          await getGeolocationAndFetch();
        }
      }
    };

    initializeWeather();

    // Actualizar cada 15 minutos en lugar de 5 para reducir carga
    const interval = setInterval(() => {
      if (mounted) {
        const lastLocation = localStorage.getItem("lastKnownLocation");
        if (lastLocation) {
          fetchWeatherDataByCity(lastLocation);
        } else {
          getGeolocationAndFetch();
        }
      }
    }, 15 * 60 * 1000);

    return () => {
      mounted = false;
      clearInterval(interval);
      console.log("Componente desmontado, limpiando recursos");
    };
  }, []);

  const handleRefresh = () => {
    console.log("Se presionó el botón Actualizar"); // Depuración
    getGeolocationAndFetch();
  };

  const handleUnitChange = (
    _event: React.MouseEvent<HTMLElement>,
    newUnit: "c" | "f" | null
  ) => {
    if (newUnit !== null) {
      setTempUnit(newUnit);
    }
  };

  const convertTemp = (tempC: number) => {
    if (typeof tempC !== "number") {
      console.error("Temperatura inválida recibida:", tempC);
      return 0;
    }
    const converted =
      tempUnit === "c" ? Math.round(tempC) : Math.round((tempC * 9) / 5 + 32);
    console.log(
      `Convirtiendo temperatura: ${tempC}°C a ${converted}${tempUnit.toUpperCase()}`
    );
    return converted;
  };

  if (loading) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        gap={2}
        minHeight={200}
        justifyContent="center"
      >
        <CircularProgress sx={{ color: "#2196F3" }} />
        <Typography sx={{ color: "#2196F3" }}>
          Cargando datos del clima...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <StyledContainer>
        <Typography variant="h6" component="div" color="error">
          {error}
        </Typography>
        <Button
          variant="contained"
          startIcon={<RefreshIcon />}
          sx={{ mt: 2 }}
          onClick={handleRefresh}
        >
          Intentar de nuevo
        </Button>
      </StyledContainer>
    );
  }

  if (!weatherData) {
    return null;
  }

  const { current, forecast } = weatherData;
  const unitSymbol = tempUnit === "c" ? "°C" : "°F";

  return (
    <StyledContainer elevation={0}>
      {/* Header with title and controls */}
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        mb={2}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: "600",
            color: "#333",
            fontSize: "18px",
          }}
        >
          ☀️ Clima Actual
        </Typography>
        <ToggleButtonGroup
          value={tempUnit}
          exclusive
          onChange={handleUnitChange}
          size="small"
          sx={{
            "& .MuiToggleButton-root": {
              border: "1px solid #e0e0e0",
              fontSize: "12px",
              padding: "4px 8px",
            },
          }}
        >
          <ToggleButton value="c">°C</ToggleButton>
          <ToggleButton value="f">°F</ToggleButton>
        </ToggleButtonGroup>
      </Box>

      {/* Current Weather */}
      <Box display="flex" alignItems="center" gap={2} mb={3}>
        {getWeatherIcon(current.condition.text)}
        <Box>
          <Typography
            variant="h1"
            sx={{
              fontSize: "48px",
              fontWeight: "300",
              color: "#333",
              lineHeight: 1,
              mb: 0.5,
            }}
          >
            {convertTemp(current.temp_c)}
            {unitSymbol}
          </Typography>
          <Typography
            variant="body1"
            sx={{
              color: "#666",
              fontSize: "14px",
            }}
          >
            {current.condition.text}
          </Typography>
        </Box>
      </Box>

      {/* Weather Details Grid */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={6}>
          <WeatherDetailItem>
            <OpacityIcon sx={{ fontSize: 16, color: "#666" }} />
            <Box>
              <Typography
                variant="body2"
                sx={{ color: "#999", fontSize: "12px" }}
              >
                Humedad
              </Typography>
              <Typography
                variant="body1"
                sx={{ color: "#333", fontSize: "14px", fontWeight: "500" }}
              >
                {current.humidity}%
              </Typography>
            </Box>
          </WeatherDetailItem>
        </Grid>
        <Grid item xs={6}>
          <WeatherDetailItem>
            <AirIcon sx={{ fontSize: 16, color: "#666" }} />
            <Box>
              <Typography
                variant="body2"
                sx={{ color: "#999", fontSize: "12px" }}
              >
                Viento
              </Typography>
              <Typography
                variant="body1"
                sx={{ color: "#333", fontSize: "14px", fontWeight: "500" }}
              >
                {Math.round(current.wind_kph)} km/h
              </Typography>
            </Box>
          </WeatherDetailItem>
        </Grid>
        <Grid item xs={6}>
          <WeatherDetailItem>
            <VisibilityIcon sx={{ fontSize: 16, color: "#666" }} />
            <Box>
              <Typography
                variant="body2"
                sx={{ color: "#999", fontSize: "12px" }}
              >
                Visibilidad
              </Typography>
              <Typography
                variant="body1"
                sx={{ color: "#333", fontSize: "14px", fontWeight: "500" }}
              >
                {Math.round(current.vis_km)} km
              </Typography>
            </Box>
          </WeatherDetailItem>
        </Grid>
        <Grid item xs={6}>
          <WeatherDetailItem>
            <ThermostatIcon sx={{ fontSize: 16, color: "#666" }} />
            <Box>
              <Typography
                variant="body2"
                sx={{ color: "#999", fontSize: "12px" }}
              >
                Sensación
              </Typography>
              <Typography
                variant="body1"
                sx={{ color: "#333", fontSize: "14px", fontWeight: "500" }}
              >
                {convertTemp(current.feelslike_c)}
                {unitSymbol}
              </Typography>
            </Box>
          </WeatherDetailItem>
        </Grid>
      </Grid>

      {/* Forecast Section */}
      <Typography
        variant="h6"
        sx={{
          fontWeight: "600",
          color: "#333",
          fontSize: "16px",
          mb: 2,
        }}
      >
        Pronóstico 4 días
      </Typography>

      <ForecastContainer>
        {forecast.forecastday.slice(0, 4).map((day, index) => {
          const isToday = index === 0;
          const dayName = isToday
            ? "Hoy"
            : index === 1
            ? "Mañana"
            : index === 2
            ? "Miércoles"
            : "Jueves";

          return (
            <ForecastCard key={index}>
              <Box display="flex" alignItems="center" gap={1}>
                {getWeatherIcon(day.day.condition.text)}
                <Typography
                  variant="body1"
                  sx={{
                    color: "#333",
                    fontSize: "14px",
                    fontWeight: "500",
                    minWidth: "80px",
                  }}
                >
                  {dayName}
                </Typography>
              </Box>

              <Box display="flex" alignItems="center" gap={2}>
                <Box
                  sx={{
                    backgroundColor:
                      day.day.daily_chance_of_rain > 50 ? "#4FC3F7" : "#E0E0E0",
                    color: day.day.daily_chance_of_rain > 50 ? "white" : "#666",
                    borderRadius: "12px",
                    padding: "2px 8px",
                    fontSize: "12px",
                    fontWeight: "500",
                    minWidth: "35px",
                    textAlign: "center",
                  }}
                >
                  {day.day.daily_chance_of_rain || 0}%
                </Box>
                <Typography
                  variant="body1"
                  sx={{
                    color: "#333",
                    fontSize: "14px",
                    fontWeight: "500",
                    minWidth: "60px",
                    textAlign: "right",
                  }}
                >
                  {convertTemp(day.day.maxtemp_c)}° /{" "}
                  {convertTemp(day.day.mintemp_c)}°
                </Typography>
              </Box>
            </ForecastCard>
          );
        })}
      </ForecastContainer>

      {/* Refresh Button */}
      <Box display="flex" justifyContent="center" mt={2}>
        <Button
          variant="text"
          startIcon={<RefreshIcon />}
          onClick={handleRefresh}
          sx={{
            color: "#666",
            fontSize: "12px",
            textTransform: "none",
            "&:hover": {
              backgroundColor: "rgba(0,0,0,0.04)",
            },
          }}
        >
          Actualizar
        </Button>
      </Box>
    </StyledContainer>
  );
};

export default WeatherDashboard;
