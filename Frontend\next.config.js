/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    unoptimized: true,
    remotePatterns: [],
    domains: [],
  },
  // Configuración específica para Windows y estabilidad
  experimental: {
    esmExternals: false,
  },
  // Configuración para evitar problemas de permisos y enlaces simbólicos
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    // Evitar problemas de caché en Windows
    config.watchOptions = {
      poll: 1000,
      aggregateTimeout: 300,
    };
    return config;
  },
  // Configuración adicional para estabilidad
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 2,
  },
  // Evitar problemas con archivos de trace
  generateBuildId: async () => {
    return "build-" + Date.now();
  },
};

module.exports = nextConfig;
