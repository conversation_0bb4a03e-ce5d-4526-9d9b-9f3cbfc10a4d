"use client";
import React from 'react';
import { Box, Skeleton, Card, CardContent } from '@mui/material';
import GridLegacy from "@mui/material/GridLegacy";

interface OptimizedSkeletonProps {
  variant: 'kpi' | 'farm' | 'weather' | 'dashboard';
  count?: number;
}

export const OptimizedSkeleton: React.FC<OptimizedSkeletonProps> = ({ 
  variant, 
  count = 1 
}) => {
  switch (variant) {
    case 'kpi':
      return (
        <GridLegacy container spacing={2}>
          {Array.from({ length: count }).map((_, index) => (
            <GridLegacy item xs={12} sm={6} md={4} lg={2} key={index}>
              <Card elevation={2} sx={{ height: '140px' }}>
                <CardContent>
                  <Skeleton variant="text" width="60%" height={20} />
                  <Skeleton variant="text" width="40%" height={32} sx={{ mt: 1 }} />
                  <Skeleton variant="text" width="80%" height={16} sx={{ mt: 1 }} />
                  <Skeleton variant="rectangular" width="100%" height={20} sx={{ mt: 1 }} />
                </CardContent>
              </Card>
            </GridLegacy>
          ))}
        </GridLegacy>
      );

    case 'farm':
      return (
        <Box
          sx={{
            bgcolor: "#F1F8E9",
            p: 2,
            borderRadius: 2,
            border: "1px solid #C5E1A5",
            minHeight: "200px",
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Skeleton variant="text" width="40%" height={24} />
            <Skeleton variant="text" width="15%" height={20} />
          </Box>
          
          {Array.from({ length: 2 }).map((_, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Card sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Skeleton variant="circular" width={24} height={24} sx={{ mr: 1.5 }} />
                  <Skeleton variant="text" width="60%" height={20} />
                </Box>
                <Box sx={{ ml: 5 }}>
                  <Skeleton variant="text" width="40%" height={16} />
                  <Skeleton variant="text" width="50%" height={16} />
                </Box>
              </Card>
            </Box>
          ))}
        </Box>
      );

    case 'weather':
      return (
        <Box
          sx={{
            bgcolor: "#F1F8E9",
            p: 2,
            borderRadius: 2,
            border: "1px solid #C5E1A5",
            height: "100%",
            maxHeight: "400px",
          }}
        >
          <Skeleton variant="text" width="60%" height={24} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" height={120} sx={{ mb: 2, borderRadius: 1 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Skeleton variant="text" width="30%" height={16} />
            <Skeleton variant="text" width="30%" height={16} />
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Skeleton variant="text" width="40%" height={16} />
            <Skeleton variant="text" width="25%" height={16} />
          </Box>
          <Skeleton variant="text" width="80%" height={16} />
        </Box>
      );

    case 'dashboard':
      return (
        <Box>
          {/* Header skeleton */}
          <Box sx={{ mb: 4 }}>
            <Skeleton variant="text" width="60%" height={40} sx={{ mb: 1 }} />
            <Skeleton variant="text" width="40%" height={24} />
          </Box>

          {/* KPI section */}
          <Box sx={{ mb: 3 }}>
            <Skeleton variant="text" width="30%" height={32} sx={{ mb: 2 }} />
            <OptimizedSkeleton variant="kpi" count={6} />
          </Box>

          {/* Content section */}
          <GridLegacy container spacing={3}>
            <GridLegacy item xs={12} md={8}>
              <OptimizedSkeleton variant="farm" />
            </GridLegacy>
            <GridLegacy item xs={12} md={4}>
              <OptimizedSkeleton variant="weather" />
            </GridLegacy>
          </GridLegacy>
        </Box>
      );

    default:
      return <Skeleton variant="rectangular" width="100%" height={200} />;
  }
};

export default OptimizedSkeleton;
