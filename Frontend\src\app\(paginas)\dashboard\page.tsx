"use client";
import { useEffect, useState } from "react";
import { Typography, Box, Skeleton, Grid } from "@mui/material";

import WeatherWidget from "../../components/weather/WeatherWidget";
import FarmSummary from "../../components/farm/FarmSummary";
import TaskList from "../../components/tasks/TaskList";
import { KpiGrid } from "../../components/kpi/KpiComponente";
import OptimizedSkeleton from "../../components/loading/OptimizedSkeleton";
import apiService from "../../../utiles/apiService";

const Dashboard = () => {
  // Estado de carga simplificado
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState<any>(null);

  // Efecto para cargar datos del dashboard de forma optimizada
  useEffect(() => {
    const loadDashboard = async () => {
      setLoading(true);
      try {
        // Precargar datos críticos en paralelo
        await apiService.preloadCriticalData();

        // Simular un pequeño delay solo para mostrar la transición suave
        await new Promise((resolve) => setTimeout(resolve, 300));

        setDashboardData({ loaded: true });
      } catch (error) {
        console.error("Error loading dashboard:", error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboard();
  }, []);

  const formatearFecha = (fecha: Date) => {
    const fechaFormateada = fecha.toLocaleDateString("es-ES", {
      weekday: "long",
      day: "numeric",
      month: "long",
      year: "numeric",
    });

    // Dividir la cadena en palabras
    const palabras = fechaFormateada.split(" ");

    // Capitalizar la primera letra del día y del mes
    palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana
    palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes

    // Unir las palabras de nuevo
    return palabras.join(" ");
  };

  // Añadir esta función para determinar la estación actual
  const obtenerEstacionActual = () => {
    const fecha = new Date();
    const mes = fecha.getMonth() + 1; // getMonth() devuelve 0-11
    const dia = fecha.getDate();

    // Verano: 21 de diciembre - 20 de marzo
    if ((mes === 12 && dia >= 21) || mes <= 2 || (mes === 3 && dia <= 20)) {
      return "Verano";
    }
    // Otoño: 21 de marzo - 20 de junio
    else if ((mes === 3 && dia >= 21) || mes <= 5 || (mes === 6 && dia <= 20)) {
      return "Otoño";
    }
    // Invierno: 21 de junio - 20 de septiembre
    else if ((mes === 6 && dia >= 21) || mes <= 8 || (mes === 9 && dia <= 20)) {
      return "Invierno";
    }
    // Primavera: 21 de septiembre - 20 de diciembre
    else {
      return "Primavera";
    }
  };

  // Función para determinar el ciclo agrícola
  const obtenerCicloAgricola = () => {
    const estacion = obtenerEstacionActual();
    return estacion === "Otoño" || estacion === "Invierno"
      ? "Otoño-Invierno"
      : "Primavera-Verano";
  };

  // Función para formatear la fecha actual
  const formatearFechaActual = () => {
    const fecha = new Date();
    const fechaFormateada = fecha.toLocaleDateString("es-ES", {
      weekday: "long",
      day: "numeric",
      month: "long",
    });

    // Dividir la cadena en palabras y capitalizar
    const palabras = fechaFormateada.split(" ");
    palabras[0] = palabras[0].charAt(0).toUpperCase() + palabras[0].slice(1); // día de la semana
    palabras[3] = palabras[3].charAt(0).toUpperCase() + palabras[3].slice(1); // mes

    return palabras.join(" ");
  };

  // Función para generar datos de sparkline simulados
  const generarSparklineData = (base: number, variacion: number = 5) => {
    return Array.from({ length: 10 }).map((_, i) => ({
      value:
        Math.round((base + Math.sin(i / 2) * variacion + i * 0.3) * 10) / 10,
    }));
  };

  // Función para generar los datos de KPI
  const generarDatosKPI = () => {
    return [
      {
        id: "temporada",
        title: "Temporada Actual",
        value: obtenerEstacionActual(),
        caption: `${obtenerCicloAgricola()} - ${formatearFechaActual()}`,

        sparklineData: generarSparklineData(70, 3),
        loading: loading,
      },
      {
        id: "hectareas",
        title: "Hectáreas Gestionadas",
        value: "1,250 ha",
        delta: 8.5,
        caption: "vs mes anterior",

        sparklineData: generarSparklineData(100, 8),
        loading: loading,
      },
      {
        id: "servicios",
        title: "Servicios Activos",
        value: "24",
        delta: 12.3,
        caption: "servicios en curso",

        sparklineData: generarSparklineData(20, 4),
        loading: loading,
      },
      {
        id: "productividad",
        title: "Productividad",
        value: "94%",
        delta: 5.2,
        caption: "eficiencia promedio",

        sparklineData: generarSparklineData(90, 6),
        loading: loading,
      },
      {
        id: "cultivos",
        title: "Cultivos Monitoreados",
        value: "8 tipos",
        delta: -2.1,
        caption: "variedades activas",

        sparklineData: generarSparklineData(8, 2),
        loading: loading,
      },
      {
        id: "clima",
        title: "Condiciones Climáticas",
        value: "Favorable",
        caption: "para actividades agrícolas",

        sparklineData: generarSparklineData(75, 10),
        loading: loading,
      },
    ];
  };

  // Mostrar skeleton mientras carga
  if (loading) {
    return (
      <Box sx={{ padding: "16px" }}>
        <OptimizedSkeleton variant="dashboard" />
      </Box>
    );
  }

  return (
    <Box sx={{ padding: "16px" }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            mb: 3,
          }}
        >
          <Typography
            variant="h6"
            fontWeight="bold"
            sx={{
              color: "#2E7D32",
              fontFamily: "Lexend, sans-serif",
              fontSize: { xs: "1.3rem", sm: "1.6rem", md: "1.9rem" },
              lineHeight: 1.2,
              whiteSpace: "nowrap",
              mb: 1,
            }}
          >
            Bienvenido al Dashboard Agropecuario
          </Typography>
          <Typography
            variant="subtitle1"
            sx={{
              color: "#666",
              fontFamily: "Inter",
              fontSize: { xs: "0.9rem", sm: "1rem", md: "1.1rem" },
              lineHeight: 1.3,
            }}
          >
            Gestiona sus Servicios de forma inteligente y eficiente
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Sección de KPI Cards */}
        <Grid size={12}>
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="h6"
              sx={{
                color: "#2E7D32",
                fontFamily: "Lexend, sans-serif",
                fontWeight: 600,
                mb: 2,
              }}
            >
              Métricas Principales
            </Typography>
            <KpiGrid
              items={generarDatosKPI()}
              columns={{ xs: 12, sm: 6, md: 4, lg: 2 }}
            />
          </Box>
        </Grid>

        {/* Sección de Farm Summary y Weather Widget */}
        <Grid size={12}>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 8 }}>
              <FarmSummary />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
              <WeatherWidget />
            </Grid>
          </Grid>
        </Grid>

        {/* Sección de TaskList */}
        <Grid size={12}>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 8 }}>
              {loading ? (
                <Box
                  sx={{
                    bgcolor: "#F1F8E9",
                    p: 2,
                    borderRadius: 2,
                    border: "1px solid #C5E1A5",
                    height: "100%",
                  }}
                >
                  {/* Header skeleton */}
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      pb: 2,
                      borderBottom: "1px solid #e0e0e0",
                    }}
                  >
                    <Skeleton variant="text" width="40%" height={32} />
                    <Skeleton variant="text" width="15%" height={24} />
                  </Box>

                  {/* Tasks skeleton */}
                  <Box sx={{ mt: 2 }}>
                    {[1, 2, 3, 4, 5].map((item) => (
                      <Box
                        key={item}
                        sx={{
                          mb: 2,
                          p: 2,
                          bgcolor: "#f8fafc",
                          borderRadius: 2,
                        }}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                          }}
                        >
                          <Box sx={{ display: "flex", gap: 1, width: "70%" }}>
                            <Skeleton
                              variant="circular"
                              width={24}
                              height={24}
                            />
                            <Box sx={{ flex: 1 }}>
                              <Skeleton
                                variant="text"
                                width="80%"
                                height={24}
                              />
                              <Skeleton
                                variant="text"
                                width="60%"
                                height={20}
                              />
                            </Box>
                          </Box>
                          <Box
                            sx={{
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "flex-end",
                              gap: 1,
                            }}
                          >
                            <Skeleton
                              variant="rectangular"
                              width={80}
                              height={24}
                              sx={{ borderRadius: 1 }}
                            />
                            <Skeleton variant="text" width={100} height={20} />
                          </Box>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </Box>
              ) : (
                <TaskList limit={5} />
              )}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
